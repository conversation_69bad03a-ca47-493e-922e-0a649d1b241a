import React from 'react';

import {
  BlockView,
  ColorsV2,
  <PERSON><PERSON>t,
  <PERSON><PERSON><PERSON>elper,
  IconAssets,
  IconImage,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

export interface LocationEmptyProps {
  testID?: string;
  label?: string;
}

const SIZE_ICON = DeviceHelper.WINDOW.WIDTH * 0.2;

export const LocationEmpty: React.FC<LocationEmptyProps> = ({ 
  testID = 'location-empty', 
  label 
}) => {
  const { t } = useI18n();

  return (
    <BlockView 
      center 
      padding={{ vertical: Spacing.SPACE_08 }}
      testID={testID}
    >
      <IconImage 
        source={IconAssets.icLocation} 
        size={SIZE_ICON}
        color={ColorsV2.neutral400}
      />
      
      <SizedBox height={Spacing.SPACE_16} />
      
      <CText 
        testID={`${testID}-text`}
        color={ColorsV2.neutral600} 
        center
      >
        {label || t('LOCATION_EMPTY')}
      </CText>
    </BlockView>
  );
};
