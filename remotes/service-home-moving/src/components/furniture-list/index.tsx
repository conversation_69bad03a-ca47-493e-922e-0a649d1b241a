import React from 'react';
import isEmpty from 'lodash/isEmpty';

import {
  BlockView,
  ColorsV2,
  CText,
  getTextWithLocale,
  IconAssets,
  IconImage,
  IFurnitureItem,
  Maybe,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { DetailInfo } from '../home-detail-info';
import { useI18n } from '@hooks';

import { styles } from './styles';

export interface FurnitureListProps {
  furniture?: Maybe<IFurnitureItem[]>;
  testID?: string;
}

export const FurnitureList: React.FC<FurnitureListProps> = ({ 
  furniture,
  testID = 'furniture-list'
}) => {
  const { t } = useI18n();
  const { locale } = usePostTaskStore();

  // Early return with null check
  if (isEmpty(furniture)) {
    return null;
  }

  return (
    <BlockView testID={testID}>
      <CText 
        bold 
        color={ColorsV2.orange500}
        testID="furniture-list-title"
      >
        {t('LABEL_OVER_FURNITURE')}
      </CText>
      
      <BlockView row style={styles.containerItem}>
        <IconImage 
          source={IconAssets.icBox} 
          size={20}
          style={styles.icon} 
        />
        
        <BlockView>
          {furniture?.map?.((item, index) => {
            const furnitureText = item?.text 
              ? getTextWithLocale(item.text, locale) 
              : '';
              
            return (
              <DetailInfo
                key={index.toString()}
                testID={`furniture-item-${index}`}
                value={furnitureText}
                quantity={item?.quantity}
              />
            );
          })}
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
