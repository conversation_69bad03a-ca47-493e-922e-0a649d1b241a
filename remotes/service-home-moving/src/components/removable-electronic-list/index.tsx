import React from 'react';
import isEmpty from 'lodash/isEmpty';

import {
  BlockView,
  ColorsV2,
  CText,
  getTextWithLocale,
  IconAssets,
  IconImage,
  IRemovableElectronic,
  Maybe,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { DetailInfo } from '../home-detail-info';
import { useI18n } from '@hooks';

import { styles } from './styles';

export interface RemovableElectronicListProps {
  removableElectronic?: Maybe<IRemovableElectronic[]>;
  testID?: string;
}

export const RemovableElectronicList: React.FC<RemovableElectronicListProps> = ({ 
  removableElectronic,
  testID = 'removable-electronic-list'
}) => {
  const { t } = useI18n();
  const { locale } = usePostTaskStore();

  // Early return with null check
  if (isEmpty(removableElectronic)) {
    return null;
  }

  return (
    <BlockView testID={testID}>
      <CText 
        bold 
        color={ColorsV2.orange500}
        testID="removable-electronic-list-title"
      >
        {t('INSTALL')}
      </CText>
      
      <BlockView row style={styles.containerItem}>
        <IconImage 
          source={IconAssets.icSetting} 
          size={20}
          style={styles.icon} 
        />
        
        <BlockView>
          {removableElectronic?.map?.((item, index) => {
            const electronicText = item?.text 
              ? getTextWithLocale(item.text, locale) 
              : '';
              
            return (
              <DetailInfo
                key={index.toString()}
                testID={`electronic-item-${index}`}
                value={electronicText}
                quantity={item?.quantity}
              />
            );
          })}
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
